<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TestCraft - 测试用例管理系统</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-blue: #007AFF;
            --primary-purple: #5856D6;
            --primary-teal: #32D74B;
            --primary-orange: #FF9500;
            --primary-red: #FF3B30;
            --primary-pink: #FF2D92;
            --text-primary: #1D1D1F;
            --text-secondary: #86868B;
            --surface-primary: rgba(255, 255, 255, 0.95);
            --surface-secondary: rgba(255, 255, 255, 0.8);
            --surface-tertiary: rgba(255, 255, 255, 0.6);
            --border-color: rgba(0, 0, 0, 0.1);
            --shadow-small: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.12);
            --shadow-large: 0 20px 60px rgba(0, 0, 0, 0.15);
            --blur-strong: blur(20px);
            --blur-medium: blur(10px);
            --radius-small: 8px;
            --radius-medium: 12px;
            --radius-large: 20px;
            --radius-xlarge: 28px;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #000;
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* 星空背景 */
        .starfield {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(ellipse at bottom, #1B2735 0%, #090A0F 100%);
            z-index: -2;
        }

        .starfield::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: sparkle 20s linear infinite;
        }

        @keyframes sparkle {
            from { transform: translateX(0); }
            to { transform: translateX(-200px); }
        }

        /* 流星效果 */
        .meteor {
            position: absolute;
            width: 300px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #fff, transparent);
            border-radius: 50px;
            animation: meteor 3s linear infinite;
            opacity: 0;
        }

        .meteor:nth-child(1) {
            top: 10%;
            left: -300px;
            animation-delay: 0s;
        }

        .meteor:nth-child(2) {
            top: 30%;
            left: -300px;
            animation-delay: 2s;
        }

        .meteor:nth-child(3) {
            top: 60%;
            left: -300px;
            animation-delay: 4s;
        }

        @keyframes meteor {
            0% {
                transform: translateX(0) translateY(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateX(calc(100vw + 300px)) translateY(300px);
                opacity: 0;
            }
        }

        /* 主容器 */
        .main-container {
            position: relative;
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
            z-index: 1;
        }

        /* 头部区域 */
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-purple));
            border-radius: 2px;
        }

        .header h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            background: linear-gradient(135deg, #fff 0%, #e1e1e6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 16px;
            letter-spacing: -0.02em;
        }

        .header p {
            font-size: 1.125rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        /* 控制面板 */
        .controls-panel {
            background: var(--surface-primary);
            backdrop-filter: var(--blur-strong);
            border-radius: var(--radius-xlarge);
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-large);
            border: 1px solid var(--border-color);
        }

        .controls-row {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .controls-row:last-child {
            margin-bottom: 0;
        }

        .control-group {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 10px 16px;
            border: none;
            border-radius: var(--radius-medium);
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            font-family: inherit;
            text-decoration: none;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            white-space: nowrap;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .btn:hover::before {
            opacity: 1;
        }

        .btn:active {
            transform: scale(0.98);
        }

        .btn-primary {
            background: var(--primary-blue);
            color: white;
        }

        .btn-success {
            background: var(--primary-teal);
            color: white;
        }

        .btn-warning {
            background: var(--primary-orange);
            color: white;
        }

        .btn-danger {
            background: var(--primary-red);
            color: white;
        }

        .btn-purple {
            background: var(--primary-purple);
            color: white;
        }

        .btn-secondary {
            background: rgba(0, 0, 0, 0.05);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        /* 文件上传 */
        .file-input {
            display: none;
        }

        .file-label {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 10px 16px;
            background: var(--primary-purple);
            color: white;
            border-radius: var(--radius-medium);
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .file-label::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .file-label:hover::before {
            opacity: 1;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
        }

        .stat-card {
            background: var(--surface-secondary);
            backdrop-filter: var(--blur-medium);
            border-radius: var(--radius-medium);
            padding: 16px 12px;
            text-align: center;
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--accent-color);
        }

        .stat-card.total::before { background: var(--primary-blue); }
        .stat-card.tested::before { background: var(--primary-purple); }
        .stat-card.not-tested::before { background: var(--text-secondary); }
        .stat-card.passed::before { background: var(--primary-teal); }
        .stat-card.failed::before { background: var(--primary-red); }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .stat-icon {
            font-size: 20px;
            margin-bottom: 6px;
            display: block;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 3px;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* 内容区域 */
        .content-panel {
            background: var(--surface-primary);
            backdrop-filter: var(--blur-strong);
            border-radius: var(--radius-xlarge);
            padding: 32px;
            box-shadow: var(--shadow-large);
            border: 1px solid var(--border-color);
        }

        /* 筛选区域 */
        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 24px;
            border-bottom: 1px solid var(--border-color);
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            white-space: nowrap;
        }

        .filter-select, .search-input {
            padding: 10px 16px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-small);
            font-size: 14px;
            font-family: inherit;
            background: white;
            color: var(--text-primary);
            transition: all 0.2s ease;
        }

        .filter-select:focus, .search-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .search-input {
            min-width: 200px;
        }

        /* 表格容器 */
        .table-container {
            border-radius: var(--radius-large);
            overflow: hidden;
            border: 1px solid var(--border-color);
            background: white;
        }

        .test-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .test-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: var(--text-primary);
            padding: 14px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            position: sticky;
            top: 0;
            z-index: 10;
            border-bottom: 2px solid var(--border-color);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
        }

        .test-table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-purple));
        }

        .test-table th:first-child {
            border-top-left-radius: var(--radius-small);
        }

        .test-table th:last-child {
            border-top-right-radius: var(--radius-small);
        }

        .test-table td {
            padding: 14px 12px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            vertical-align: top;
            line-height: 1.4;
        }

        .test-table tr {
            transition: all 0.2s ease;
        }

        .test-table tr:hover {
            background: rgba(0, 122, 255, 0.02);
        }

        .test-table tr.tested-success {
            background: rgba(50, 215, 75, 0.03);
            border-left: 3px solid var(--primary-teal);
        }

        .test-table tr.tested-failed {
            background: rgba(255, 59, 48, 0.03);
            border-left: 3px solid var(--primary-red);
        }

        /* 表格单元格样式 */
        .test-id {
            font-weight: 600;
            color: var(--primary-blue);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 13px;
            min-width: 80px;
        }

        .test-scenario {
            font-weight: 500;
            color: var(--text-primary);
            min-width: 200px;
        }

        .test-content {
            max-width: 250px;
            word-wrap: break-word;
            white-space: pre-wrap;
            color: var(--text-secondary);
            font-size: 13px;
        }

        .priority {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
            min-width: 32px;
            text-align: center;
        }

        .priority.P0 {
            background: rgba(255, 59, 48, 0.1);
            color: var(--primary-red);
            border: 1px solid rgba(255, 59, 48, 0.2);
        }

        .priority.P1 {
            background: rgba(255, 149, 0, 0.1);
            color: var(--primary-orange);
            border: 1px solid rgba(255, 149, 0, 0.2);
        }

        .priority.P2 {
            background: rgba(142, 142, 147, 0.1);
            color: var(--text-secondary);
            border: 1px solid rgba(142, 142, 147, 0.2);
        }

        /* 复选框和按钮 */
        .checkbox-cell, .test-status-cell {
            text-align: center;
            min-width: 80px;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
            accent-color: var(--primary-blue);
        }

        .status-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 10px;
            font-weight: 600;
            min-width: 55px;
            transition: all 0.2s ease;
            font-family: inherit;
        }

        .status-btn.success {
            background: rgba(50, 215, 75, 0.1);
            color: var(--primary-teal);
            border: 1px solid rgba(50, 215, 75, 0.3);
        }

        .status-btn.fail {
            background: rgba(255, 59, 48, 0.1);
            color: var(--primary-red);
            border: 1px solid rgba(255, 59, 48, 0.3);
        }

        .status-btn.not-tested {
            background: rgba(142, 142, 147, 0.1);
            color: var(--text-secondary);
            border: 1px solid rgba(142, 142, 147, 0.3);
        }

        .status-btn:hover:not(:disabled) {
            transform: scale(1.05);
            opacity: 0.8;
        }

        .status-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none;
        }

        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
        }

        .modal-content {
            background: var(--surface-primary);
            backdrop-filter: var(--blur-strong);
            margin: 5% auto;
            padding: 32px;
            border-radius: var(--radius-xlarge);
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: var(--shadow-large);
            border: 1px solid var(--border-color);
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .close {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.05);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: var(--text-secondary);
            transition: all 0.2s ease;
        }

        .close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: var(--text-primary);
        }

        .detail-item {
            margin-bottom: 20px;
        }

        .detail-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
        }

        .detail-content {
            background: rgba(0, 0, 0, 0.02);
            padding: 12px 16px;
            border-radius: var(--radius-small);
            white-space: pre-wrap;
            border-left: 3px solid var(--primary-blue);
            font-size: 14px;
            line-height: 1.5;
            color: var(--text-secondary);
        }

        /* 状态提示 */
        .loading, .no-data {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
            font-size: 16px;
        }

        .loading::before {
            content: '⚡';
            font-size: 2rem;
            display: block;
            margin-bottom: 16px;
            animation: pulse 2s ease-in-out infinite;
        }

        .no-data::before {
            content: '📋';
            font-size: 2rem;
            display: block;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 24px;
            border-radius: var(--radius-medium);
            color: white;
            font-weight: 500;
            z-index: 9999;
            backdrop-filter: var(--blur-medium);
            box-shadow: var(--shadow-medium);
            border: 1px solid rgba(255, 255, 255, 0.1);
            animation: slideInRight 0.3s ease;
        }

        .notification.success {
            background: rgba(50, 215, 75, 0.9);
        }

        .notification.error {
            background: rgba(255, 59, 48, 0.9);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                padding: 20px 16px;
            }

            .controls-panel, .content-panel {
                padding: 20px;
                border-radius: var(--radius-large);
            }

            .controls-row {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .control-group {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .filters {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .search-input {
                min-width: auto;
                width: 100%;
            }

            .test-table {
                font-size: 12px;
            }

            .test-table th,
            .test-table td {
                padding: 12px 8px;
            }

            .test-content {
                max-width: 150px;
            }

            .modal-content {
                margin: 10% auto;
                padding: 24px;
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <!-- 详情模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>测试用例详情</h2>
                <button class="close" onclick="closeModal()">×</button>
            </div>
            <div id="modalBody"></div>
        </div>
    </div>

    <script>
        let testCases = [];
        let filteredTestCases = [];

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadDefaultData();
            setupFileInput();
        });

        // 设置文件输入监听
        function setupFileInput() {
            document.getElementById('jsonFile').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const data = JSON.parse(e.target.result);
                            loadTestCases(data);
                            showNotification('✅ JSON文件导入成功！', 'success');
                        } catch (error) {
                            showNotification('❌ JSON文件格式错误！', 'error');
                            console.error('JSON解析错误:', error);
                        }
                    };
                    reader.readAsText(file);
                }
            });
        }

        // 加载默认数据
        function loadDefaultData() {
            // 空的默认数据
            const defaultData = [];
            
            // 检查是否有本地存储的数据
            const savedData = localStorage.getItem('testCases');
            if (savedData) {
                try {
                    loadTestCases(JSON.parse(savedData));
                } catch (error) {
                    loadTestCases(defaultData);
                }
            } else {
                loadTestCases(defaultData);
            }
        }

        // 加载测试用例
        function loadTestCases(data) {
            // 确保每个测试用例都有必要的属性
            testCases = data.map(testCase => ({
                ...testCase,
                tested: testCase.tested !== undefined ? testCase.tested : false,
                testResult: testCase.testResult !== undefined ? testCase.testResult : null // null: 未测试, true: 成功, false: 失败
            }));
            filteredTestCases = [...testCases];
            renderTable();
            updateStats();
            saveToLocalStorage();
        }

        // 渲染表格
        function renderTable() {
            const container = document.getElementById('tableContainer');
            
            if (filteredTestCases.length === 0) {
                container.innerHTML = '<div class="no-data">暂无测试用例数据<br><small>请导入JSON文件或添加新的测试用例</small></div>';
                return;
            }

            const table = `
                <table class="test-table">
                    <thead>
                        <tr>
                            <th>用例ID</th>
                            <th>测试场景</th>
                            <th>前置条件</th>
                            <th>测试步骤</th>
                            <th>输入数据</th>
                            <th>预期结果</th>
                            <th>优先级</th>
                            <th>是否测试</th>
                            <th>测试结果</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredTestCases.map(testCase => `
                            <tr class="${getRowClass(testCase)}">
                                <td class="test-id">${testCase.id}</td>
                                <td class="test-scenario">${testCase.scenario}</td>
                                <td class="test-content">${truncateText(testCase.preconditions, 100)}</td>
                                <td class="test-content">${truncateText(testCase.steps, 100)}</td>
                                <td class="test-content">${truncateText(testCase.inputData, 80)}</td>
                                <td class="test-content">${truncateText(testCase.expectedResult, 100)}</td>
                                <td><span class="priority ${testCase.priority}">${testCase.priority}</span></td>
                                <td class="checkbox-cell">
                                    <input type="checkbox" class="checkbox" 
                                           ${testCase.tested ? 'checked' : ''} 
                                           onchange="toggleTested('${testCase.id}')">
                                </td>
                                <td class="test-status-cell">
                                    <button class="status-btn ${getStatusClass(testCase)}" 
                                            ${!testCase.tested ? 'disabled' : ''}
                                            onclick="toggleTestResult('${testCase.id}')">
                                        ${getStatusText(testCase)}
                                    </button>
                                </td>
                                <td>
                                    <button class="btn btn-primary" style="padding: 6px 10px; font-size: 11px;" 
                                            onclick="showDetail('${testCase.id}')">
                                        <span>👁</span>
                                        详情
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }

        // 获取行样式类
        function getRowClass(testCase) {
            if (testCase.tested && testCase.testResult === true) return 'tested-success';
            if (testCase.tested && testCase.testResult === false) return 'tested-failed';
            return '';
        }

        // 截断文本
        function truncateText(text, maxLength) {
            if (!text) return '';
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        // 获取状态样式类
        function getStatusClass(testCase) {
            if (!testCase.tested) return 'not-tested';
            if (testCase.testResult === true) return 'success';
            if (testCase.testResult === false) return 'fail';
            return 'not-tested';
        }

        // 获取状态文本
        function getStatusText(testCase) {
            if (!testCase.tested) return '未测试';
            if (testCase.testResult === true) return '成功';
            if (testCase.testResult === false) return '失败';
            return '未测试';
        }

        // 切换是否测试状态
        function toggleTested(testId) {
            const testCase = testCases.find(tc => tc.id === testId);
            if (testCase) {
                testCase.tested = !testCase.tested;
                if (!testCase.tested) {
                    testCase.testResult = null; // 如果取消测试，清除测试结果
                }
                updateStats();
                renderTable();
                saveToLocalStorage();
            }
        }

        // 切换测试结果
        function toggleTestResult(testId) {
            const testCase = testCases.find(tc => tc.id === testId);
            if (testCase && testCase.tested) {
                // 循环切换：null -> true -> false -> true -> ...
                if (testCase.testResult === null) {
                    testCase.testResult = true; // 成功
                } else if (testCase.testResult === true) {
                    testCase.testResult = false; // 失败
                } else {
                    testCase.testResult = true; // 回到成功
                }
                updateStats();
                renderTable();
                saveToLocalStorage();
            }
        }

        // 更新统计
        function updateStats() {
            const total = testCases.length;
            const tested = testCases.filter(tc => tc.tested).length;
            const notTested = total - tested;
            const passed = testCases.filter(tc => tc.tested && tc.testResult === true).length;
            const failed = testCases.filter(tc => tc.tested && tc.testResult === false).length;

            document.getElementById('totalCount').textContent = total;
            document.getElementById('testedCount').textContent = tested;
            document.getElementById('notTestedCount').textContent = notTested;
            document.getElementById('passedCount').textContent = passed;
            document.getElementById('failedCount').textContent = failed;
        }

        // 筛选测试用例
        function filterTests() {
            const statusFilter = document.getElementById('statusFilter').value;
            const priorityFilter = document.getElementById('priorityFilter').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            filteredTestCases = testCases.filter(testCase => {
                // 状态筛选
                let statusMatch = false;
                if (statusFilter === 'all') {
                    statusMatch = true;
                } else if (statusFilter === 'tested') {
                    statusMatch = testCase.tested;
                } else if (statusFilter === 'not-tested') {
                    statusMatch = !testCase.tested;
                } else if (statusFilter === 'passed') {
                    statusMatch = testCase.tested && testCase.testResult === true;
                } else if (statusFilter === 'failed') {
                    statusMatch = testCase.tested && testCase.testResult === false;
                }

                // 优先级筛选
                const priorityMatch = priorityFilter === 'all' || testCase.priority === priorityFilter;

                // 搜索筛选
                const searchMatch = searchTerm === '' || 
                    (testCase.id && testCase.id.toLowerCase().includes(searchTerm)) ||
                    (testCase.scenario && testCase.scenario.toLowerCase().includes(searchTerm)) ||
                    (testCase.preconditions && testCase.preconditions.toLowerCase().includes(searchTerm)) ||
                    (testCase.steps && testCase.steps.toLowerCase().includes(searchTerm)) ||
                    (testCase.inputData && testCase.inputData.toLowerCase().includes(searchTerm)) ||
                    (testCase.expectedResult && testCase.expectedResult.toLowerCase().includes(searchTerm));

                return statusMatch && priorityMatch && searchMatch;
            });

            renderTable();
        }

        // 显示详情
        function showDetail(testId) {
            const testCase = testCases.find(tc => tc.id === testId);
            if (!testCase) return;

            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <div class="detail-item">
                    <div class="detail-label">测试用例ID</div>
                    <div class="detail-content">${testCase.id || 'N/A'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">测试场景</div>
                    <div class="detail-content">${testCase.scenario || 'N/A'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">前置条件</div>
                    <div class="detail-content">${testCase.preconditions || 'N/A'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">测试步骤</div>
                    <div class="detail-content">${testCase.steps || 'N/A'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">输入数据</div>
                    <div class="detail-content">${testCase.inputData || 'N/A'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">预期结果</div>
                    <div class="detail-content">${testCase.expectedResult || 'N/A'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">优先级</div>
                    <div class="detail-content"><span class="priority ${testCase.priority}">${testCase.priority || 'N/A'}</span></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">是否测试</div>
                    <div class="detail-content">${testCase.tested ? '✅ 已测试' : '⏳ 未测试'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">测试结果</div>
                    <div class="detail-content">${getTestResultText(testCase)}</div>
                </div>
            `;

            document.getElementById('detailModal').style.display = 'block';
        }

        // 获取测试结果文本
        function getTestResultText(testCase) {
            if (!testCase.tested) return '⏳ 未测试';
            if (testCase.testResult === true) return '✅ 测试成功';
            if (testCase.testResult === false) return '❌ 测试失败';
            return '⏳ 未测试';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('detailModal').style.display = 'none';
        }

        // 显示导出模态框
        function showExportModal(type) {
            let data, rangeText, fileName;
            
            switch(type) {
                case 'tested':
                    data = testCases.filter(tc => tc.tested);
                    rangeText = '已测试的用例';
                    fileName = '已测试用例';
                    break;
                case 'not-tested':
                    data = testCases.filter(tc => !tc.tested);
                    rangeText = '未测试的用例';
                    fileName = '未测试用例';
                    break;
                case 'all':
                    data = testCases;
                    rangeText = '全部用例';
                    fileName = '全部测试用例';
                    break;
                default:
                    return;
            }

            document.getElementById('exportRange').textContent = rangeText;
            document.getElementById('exportCount').textContent = `${data.length} 个测试用例`;
            document.getElementById('exportFileName').value = fileName;
            
            // 存储当前导出数据到全局变量
            window.currentExportData = data;
            
            document.getElementById('exportModal').style.display = 'block';
        }

        // 关闭导出模态框
        function closeExportModal() {
            document.getElementById('exportModal').style.display = 'none';
        }

        // 确认导出
        function confirmExport() {
            const fileName = document.getElementById('exportFileName').value.trim();
            if (!fileName) {
                showNotification('❌ 请输入文件名称！', 'error');
                return;
            }
            
            if (window.currentExportData) {
                exportToJson(window.currentExportData, fileName);
                closeExportModal();
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const detailModal = document.getElementById('detailModal');
            const exportModal = document.getElementById('exportModal');
            if (event.target === detailModal) {
                detailModal.style.display = 'none';
            }
            if (event.target === exportModal) {
                exportModal.style.display = 'none';
            }
        }

        // 导出功能
        function exportToJson(data, filename) {
            const jsonStr = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `${filename}_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showNotification(`✅ ${filename}导出成功！`, 'success');
        }

        // 保存到本地存储
        function saveToLocalStorage() {
            localStorage.setItem('testCases', JSON.stringify(testCases));
        }

        // 显示通知
        function showNotification(message, type) {
            // 移除现有通知
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideInRight 0.3s ease reverse';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, 3000);
        }
    </script>
</body>
</html> 星空背景 -->
    <div class="starfield">
        <div class="meteor"></div>
        <div class="meteor"></div>
        <div class="meteor"></div>
    </div>

    <div class="main-container">
        <!-- 头部 -->
        <header class="header">
            <h1>TestCraft</h1>
            <p>专业的测试用例管理系统，让测试工作更加高效优雅</p>
        </header>

        <!-- 控制面板 -->
        <div class="controls-panel">
            <div class="controls-row">
                <div class="control-group">
                    <input type="file" id="jsonFile" class="file-input" accept=".json">
                    <label for="jsonFile" class="file-label">
                        <span>📂</span>
                        导入数据
                    </label>
                    <button class="btn btn-success" onclick="showExportModal('tested')">
                        <span>✓</span>
                        导出已测试
                    </button>
                    <button class="btn btn-warning" onclick="showExportModal('not-tested')">
                        <span>⏳</span>
                        导出未测试
                    </button>
                    <button class="btn btn-purple" onclick="showExportModal('all')">
                        <span>📊</span>
                        导出全部
                    </button>
                </div>
            </div>

            <div class="controls-row">
                <div class="stats-grid">
                    <div class="stat-card total">
                        <span class="stat-icon">📋</span>
                        <div class="stat-number" id="totalCount">0</div>
                        <div class="stat-label">总计</div>
                    </div>
                    <div class="stat-card tested">
                        <span class="stat-icon">🧪</span>
                        <div class="stat-number" id="testedCount">0</div>
                        <div class="stat-label">已测试</div>
                    </div>
                    <div class="stat-card not-tested">
                        <span class="stat-icon">⏳</span>
                        <div class="stat-number" id="notTestedCount">0</div>
                        <div class="stat-label">未测试</div>
                    </div>
                    <div class="stat-card passed">
                        <span class="stat-icon">✅</span>
                        <div class="stat-number" id="passedCount">0</div>
                        <div class="stat-label">测试成功</div>
                    </div>
                    <div class="stat-card failed">
                        <span class="stat-icon">❌</span>
                        <div class="stat-number" id="failedCount">0</div>
                        <div class="stat-label">测试失败</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容面板 -->
        <div class="content-panel">
            <div class="filters">
                <div class="filter-group">
                    <label class="filter-label">状态筛选:</label>
                    <select class="filter-select" id="statusFilter" onchange="filterTests()">
                        <option value="all">全部</option>
                        <option value="tested">已测试</option>
                        <option value="not-tested">未测试</option>
                        <option value="passed">测试成功</option>
                        <option value="failed">测试失败</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">优先级:</label>
                    <select class="filter-select" id="priorityFilter" onchange="filterTests()">
                        <option value="all">全部</option>
                        <option value="P0">P0</option>
                        <option value="P1">P1</option>
                        <option value="P2">P2</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">搜索:</label>
                    <input type="text" class="search-input" id="searchInput" placeholder="搜索测试用例..." oninput="filterTests()">
                </div>
            </div>

            <div class="table-container">
                <div id="tableContainer">
                    <div class="loading">正在加载测试用例数据...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导出模态框 -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>导出测试用例</h2>
                <button class="close" onclick="closeExportModal()">×</button>
            </div>
            <div id="exportModalBody">
                <div class="detail-item">
                    <div class="detail-label">文件名称</div>
                    <input type="text" id="exportFileName" class="search-input" placeholder="请输入文件名称" style="width: 100%; margin-top: 8px;">
                </div>
                <div class="detail-item">
                    <div class="detail-label">导出范围</div>
                    <div class="detail-content" id="exportRange"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">用例数量</div>
                    <div class="detail-content" id="exportCount"></div>
                </div>
                <div style="display: flex; gap: 12px; justify-content: center; margin-top: 24px;">
                    <button class="btn btn-primary" onclick="confirmExport()">
                        <span>📄</span>
                        确认导出
                    </button>
                    <button class="btn btn-secondary" onclick="closeExportModal()">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!--